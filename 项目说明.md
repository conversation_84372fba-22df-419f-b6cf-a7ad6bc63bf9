这是一个STM32 L031G6低功耗项目，需要实现以下精确的业务流程：

**主要业务流程：**
1. **休眠唤醒阶段**：
   - 单片机处于休眠模式
   - MLX90393磁场传感器检测到磁场变化时，通过PB0引脚的INT中断唤醒单片机
   - 单片机唤醒后立即关闭MLX90393模块电源

2. **电压检测阶段**：
   - 单片机唤醒后等待2秒钟
   - 读取电池电压值并保存备用

3. **E70通信阶段**：
   - 启动E70通信模块电源
   - 进行E70模块初始化设置
   - 持续监听hlpuart1 RX端口5秒钟，检测是否收到有效数据
   - **如果收到数据**：调用E70_CheckReceiveAck()验证，然后调用E70_SendDeviceInfo()发送设备信息
   - **如果5秒内未收到数据**：关闭E70模块电源，重新初始化MLX90393模块，进入休眠模式

4. **数据发送确认机制**：
   - 发送数据后持续检查hlpuart1 RX是否收到确认回复
   - 如果超过2秒未收到确认数据，则重新发送
   - 最多重试3次，如果仍无响应则关闭E70模块电源
   - 成功发送或重试失败后，都要重新初始化MLX90393模块并进入休眠模式

**重要约束条件：**
1. **保持现有代码**：当前的E70模块代码、MLX90393模块代码和休眠代码都是正确的，不要重写这些部分
2. **E70初始化策略**：E70模块的配置设置只在单片机首次上电时执行一次，后续唤醒周期中直接作为hlpuart1串口使用，无需重复设置
3. **数据传输协议**：E70的数据发送必须遵循以下顺序：
   - 等待接收E70_CheckReceiveAck()确认
   - 调用E70_SendDeviceInfo()发送数据
   - 再次等待E70_CheckReceiveAck()确认发送成功
